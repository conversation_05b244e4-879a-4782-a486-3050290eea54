{"name": "vue-phone-manager", "version": "1.0.0", "description": "二手手机库存管理小程序 Vue.js 前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "api:mock": "node scripts/switch-api.js mock", "api:real": "node scripts/switch-api.js real", "api:status": "node scripts/switch-api.js status"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "chart.js": "^4.4.0", "vue-chartjs": "^5.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}