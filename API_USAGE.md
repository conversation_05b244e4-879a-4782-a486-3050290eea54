# API使用说明

## 概述

本项目支持Mock API和真实API两种模式，可以通过环境变量进行切换，方便开发和联调。

## 文件结构

```
src/api/
├── index.js          # API统一入口文件（推荐使用）
├── realApi.js        # 真实API调用文件
├── customer.js       # Mock顾客端API（保留）
├── merchant.js       # Mock商家端API（保留）
└── config/
    └── api.js        # API配置文件
```

## 环境配置

### 1. 使用Mock数据（默认）
```bash
# .env文件
VITE_USE_MOCK=true
VITE_API_BASE_URL=http://localhost:8080/api
```

### 2. 使用真实API（本地联调）
```bash
# .env文件
VITE_USE_MOCK=false
VITE_API_BASE_URL=http://localhost:8080/api
```

## 使用方式

### 推荐方式：使用统一API入口

```javascript
// 在Vue组件中使用
import { 
  getProducts, 
  getMerchantProfile, 
  loginByPhoneAndPassword 
} from '@/api/index.js'

// 获取商品列表
const products = await getProducts({ brand: 'Apple', page: 1, size: 10 })

// 商家登录
const result = await loginByPhoneAndPassword('15035037444', 'password123')

// 获取商家信息
const profile = await getMerchantProfile()
```

### 直接使用真实API

```javascript
// 直接使用真实API（不推荐，除非有特殊需求）
import { merchantLogin, getProducts } from '@/api/realApi.js'

const result = await merchantLogin({ phone: '15035037444', password: 'password123' })
```

## API接口对照表

### 顾客端API

| 功能 | 统一API方法 | 真实API端点 | Mock支持 |
|------|-------------|-------------|----------|
| 获取商品列表 | `getProducts(filters)` | `GET /customer/products` | ✅ |
| 获取商品详情 | `getProductDetail(id)` | `GET /customer/products/{id}` | ✅ |
| 搜索商品 | `searchProducts(keyword, filters)` | `GET /customer/products/search` | ✅ |
| 获取热门商品 | `getHotProducts(limit)` | `GET /customer/products/hot` | ✅ |
| 获取品牌列表 | `getBrands()` | `GET /customer/brands` | ✅ |

### 商家认证API

| 功能 | 统一API方法 | 真实API端点 | Mock支持 |
|------|-------------|-------------|----------|
| 商家登录 | `loginByPhoneAndPassword(phone, password)` | `POST /merchant/login` | ✅ |
| 商家注册 | `registerMerchant(data)` | `POST /merchant/register` | ✅ |
| 修改密码 | `changePassword(data)` | `POST /merchant/change-password` | ❌ |

### 商家管理API

| 功能 | 统一API方法 | 真实API端点 | Mock支持 |
|------|-------------|-------------|----------|
| 获取商家信息 | `getMerchantProfile()` | `GET /merchant/profile` | ✅ |
| 更新商家信息 | `updateMerchantProfile(data)` | `PUT /merchant/profile` | ✅ |
| 获取工作台数据 | `getDashboardData()` | `GET /merchant/dashboard` | ✅ |
| 获取库存列表 | `getInventory(filters)` | `GET /merchant/inventory` | ✅ |
| 添加库存商品 | `addInventoryItem(data)` | `POST /merchant/inventory` | ✅ |
| 更新库存商品 | `updateInventoryItem(id, data)` | `PUT /merchant/inventory/{id}` | ✅ |
| 删除库存商品 | `deleteInventoryItem(id)` | `DELETE /merchant/inventory/{id}` | ✅ |
| 售出库存商品 | `sellInventoryItem(id, data)` | `POST /merchant/inventory/{id}/sell` | ✅ |
| 获取销售统计 | `getSalesStats(period)` | `GET /merchant/stats/sales` | ✅ |
| 获取库存统计 | `getInventoryStats()` | `GET /merchant/stats/inventory` | ✅ |
| 标记通知已读 | `markNotificationsAsRead(ids)` | `POST /merchant/notifications/read` | ✅ |

### 文件管理API

| 功能 | 统一API方法 | 真实API端点 | Mock支持 |
|------|-------------|-------------|----------|
| 上传图片 | `uploadImage(formData)` | `POST /files/upload` | ⚠️ (返回模拟数据) |

## 缺少的API接口

根据你提供的API文档，以下接口可能需要补充：

### 1. 验证码相关接口（Mock中有，但文档中未明确提及）
- 发送忘记密码验证码：`POST /merchant/send-code`
- 发送注册验证码：`POST /merchant/send-register-code`
- 通过验证码重置密码：`POST /merchant/reset-password`

### 2. 分页支持确认
- Mock API返回数组，真实API应返回分页对象
- 后端返回格式应为：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  },
  "timestamp": 1704355200000
}
```

### 3. 统计数据API确认
- 工作台数据：`GET /merchant/dashboard`
- 销售统计：`GET /merchant/stats/sales?period=7d`
- 库存统计：`GET /merchant/stats/inventory`

### 4. 通知管理API
- 标记通知已读：`POST /merchant/notifications/read`

## 切换API模式

### 开发时使用Mock数据
```bash
# 修改.env文件
VITE_USE_MOCK=true
```

### 联调时使用真实API
```bash
# 修改.env文件
VITE_USE_MOCK=false
# 确保后端服务运行在 http://localhost:8080
```

## 注意事项

1. **JWT认证**：真实API需要在请求头中携带JWT令牌，已在`request.js`中自动处理
2. **错误处理**：统一的错误处理已在响应拦截器中实现
3. **数据格式**：后端返回的标准格式应为：
   ```json
   {
     "code": 200,
     "message": "操作成功", 
     "data": {},
     "timestamp": 1704355200000
   }
   ```
4. **CORS**：确保后端服务配置了正确的CORS设置
5. **环境变量**：修改`.env`文件后需要重启开发服务器

## 快速切换API模式

### 使用npm脚本（推荐）
```bash
# 切换到Mock模式
npm run api:mock

# 切换到真实API模式
npm run api:real

# 查看当前API状态
npm run api:status
```

### 手动修改.env文件
```bash
# Mock模式
VITE_USE_MOCK=true

# 真实API模式
VITE_USE_MOCK=false
```

## 测试工具

### 1. 浏览器控制台测试
开发模式下，可在浏览器控制台中使用：
```javascript
// 运行完整API测试
window.apiTest.runFullApiTest()

// 查看API配置
window.apiTest.testApiSwitch()

// 单独测试顾客端API
window.apiTest.testCustomerApi()
```

### 2. API测试组件
项目中包含了`ApiTestDemo.vue`组件，可以在页面中使用：
```vue
<template>
  <ApiTestDemo />
</template>

<script>
import ApiTestDemo from '@/components/ApiTestDemo.vue'
export default {
  components: { ApiTestDemo }
}
</script>
```

## 测试建议

1. **开发阶段**：使用Mock数据确保前端功能正常
2. **联调阶段**：切换到真实API模式，启动后端服务
3. **逐步测试**：先测试顾客端API，再测试商家端API
4. **认证测试**：确保JWT认证流程正常工作
5. **错误处理**：测试各种错误情况的处理
