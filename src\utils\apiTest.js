// File: src/utils/apiTest.js
// API测试工具 - 用于验证API切换功能

import * as api from '@/api/index.js'

/**
 * 测试顾客端API
 */
export async function testCustomerApi() {
  console.log('=== 测试顾客端API ===')
  
  try {
    // 测试获取商品列表
    console.log('1. 测试获取商品列表...')
    const products = await api.getProducts({ page: 1, size: 5 })
    console.log('✅ 获取商品列表成功:', products.length || products.records?.length, '个商品')
    
    // 测试获取商品详情
    if (products.length > 0 || products.records?.length > 0) {
      const firstProduct = products[0] || products.records[0]
      console.log('2. 测试获取商品详情...')
      const detail = await api.getProductDetail(firstProduct.id)
      console.log('✅ 获取商品详情成功:', detail.title)
    }
    
    // 测试搜索商品
    console.log('3. 测试搜索商品...')
    const searchResults = await api.searchProducts('iPhone')
    console.log('✅ 搜索商品成功:', searchResults.length || searchResults.records?.length, '个结果')
    
    // 测试获取热门商品
    console.log('4. 测试获取热门商品...')
    const hotProducts = await api.getHotProducts(3)
    console.log('✅ 获取热门商品成功:', hotProducts.length, '个商品')
    
    // 测试获取品牌列表
    console.log('5. 测试获取品牌列表...')
    const brands = await api.getBrands()
    console.log('✅ 获取品牌列表成功:', brands.length, '个品牌')
    
    return true
  } catch (error) {
    console.error('❌ 顾客端API测试失败:', error.message)
    return false
  }
}

/**
 * 测试商家认证API
 */
export async function testMerchantAuthApi() {
  console.log('=== 测试商家认证API ===')
  
  try {
    // 测试商家登录
    console.log('1. 测试商家登录...')
    const loginResult = await api.loginByPhoneAndPassword('15035037444', 'password123')
    console.log('✅ 商家登录成功:', loginResult.merchant?.storeName)
    
    return loginResult
  } catch (error) {
    console.error('❌ 商家认证API测试失败:', error.message)
    return null
  }
}

/**
 * 测试商家管理API（需要先登录）
 */
export async function testMerchantManagementApi() {
  console.log('=== 测试商家管理API ===')
  
  try {
    // 测试获取商家信息
    console.log('1. 测试获取商家信息...')
    const profile = await api.getMerchantProfile()
    console.log('✅ 获取商家信息成功:', profile.storeName)
    
    // 测试获取工作台数据
    console.log('2. 测试获取工作台数据...')
    const dashboard = await api.getDashboardData()
    console.log('✅ 获取工作台数据成功:', dashboard.todayStats)
    
    // 测试获取库存列表
    console.log('3. 测试获取库存列表...')
    const inventory = await api.getInventory({ page: 1, size: 5 })
    console.log('✅ 获取库存列表成功:', inventory.length || inventory.records?.length, '个商品')
    
    // 测试获取销售统计
    console.log('4. 测试获取销售统计...')
    const salesStats = await api.getSalesStats('7d')
    console.log('✅ 获取销售统计成功:', salesStats.totalSales)
    
    return true
  } catch (error) {
    console.error('❌ 商家管理API测试失败:', error.message)
    return false
  }
}

/**
 * 完整API测试
 */
export async function runFullApiTest() {
  console.log('🚀 开始完整API测试...')
  console.log('当前API模式:', import.meta.env.VITE_USE_MOCK === 'true' ? 'Mock模式' : '真实API模式')
  console.log('API基础URL:', import.meta.env.VITE_API_BASE_URL)
  console.log('')
  
  const results = {
    customer: false,
    merchantAuth: false,
    merchantManagement: false
  }
  
  // 测试顾客端API
  results.customer = await testCustomerApi()
  console.log('')
  
  // 测试商家认证API
  const loginResult = await testMerchantAuthApi()
  results.merchantAuth = !!loginResult
  console.log('')
  
  // 如果登录成功，测试商家管理API
  if (loginResult) {
    results.merchantManagement = await testMerchantManagementApi()
    console.log('')
  }
  
  // 输出测试结果
  console.log('=== 测试结果汇总 ===')
  console.log('顾客端API:', results.customer ? '✅ 通过' : '❌ 失败')
  console.log('商家认证API:', results.merchantAuth ? '✅ 通过' : '❌ 失败')
  console.log('商家管理API:', results.merchantManagement ? '✅ 通过' : '❌ 失败')
  
  const allPassed = Object.values(results).every(result => result)
  console.log('整体测试:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
  
  return results
}

/**
 * 测试API切换功能
 */
export function testApiSwitch() {
  console.log('=== API配置信息 ===')
  console.log('环境模式:', import.meta.env.MODE)
  console.log('使用Mock:', import.meta.env.VITE_USE_MOCK)
  console.log('API基础URL:', import.meta.env.VITE_API_BASE_URL)
  console.log('调试模式:', import.meta.env.VITE_DEBUG)
  
  // 检查环境变量配置
  const useMock = import.meta.env.VITE_USE_MOCK === 'true'
  const baseUrl = import.meta.env.VITE_API_BASE_URL
  
  console.log('')
  console.log('=== 配置建议 ===')
  if (useMock) {
    console.log('✅ 当前使用Mock数据，适合前端开发')
    console.log('💡 如需联调后端，请修改.env文件：VITE_USE_MOCK=false')
  } else {
    console.log('✅ 当前使用真实API，适合后端联调')
    console.log('💡 请确保后端服务运行在:', baseUrl)
    console.log('💡 如需使用Mock数据，请修改.env文件：VITE_USE_MOCK=true')
  }
}

// 在浏览器控制台中可以直接调用的测试函数
if (typeof window !== 'undefined') {
  window.apiTest = {
    testCustomerApi,
    testMerchantAuthApi,
    testMerchantManagementApi,
    runFullApiTest,
    testApiSwitch
  }
  
  console.log('💡 API测试工具已加载，可在控制台中使用：')
  console.log('- window.apiTest.runFullApiTest() // 运行完整测试')
  console.log('- window.apiTest.testApiSwitch() // 查看API配置')
}
