<!-- File: src/components/ApiTestDemo.vue -->
<!-- API测试演示组件 -->
<template>
  <div class="api-test-demo p-6 bg-white rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">API测试演示</h2>
    
    <!-- API配置信息 -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">当前API配置</h3>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="font-medium">模式:</span>
          <span :class="useMock ? 'text-blue-600' : 'text-green-600'" class="ml-2">
            {{ useMock ? 'Mock模式' : '真实API模式' }}
          </span>
        </div>
        <div>
          <span class="font-medium">基础URL:</span>
          <span class="ml-2 text-gray-600">{{ baseUrl }}</span>
        </div>
      </div>
    </div>
    
    <!-- 测试按钮 -->
    <div class="mb-6 flex flex-wrap gap-3">
      <button 
        @click="testCustomerApi" 
        :disabled="loading"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        测试顾客端API
      </button>
      <button 
        @click="testMerchantLogin" 
        :disabled="loading"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
      >
        测试商家登录
      </button>
      <button 
        @click="testMerchantManagement" 
        :disabled="loading || !isLoggedIn"
        class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
      >
        测试商家管理API
      </button>
      <button 
        @click="runFullTest" 
        :disabled="loading"
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
      >
        完整测试
      </button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="mb-4 text-center">
      <div class="inline-flex items-center">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
        <span>测试中...</span>
      </div>
    </div>
    
    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
      <div v-for="(result, index) in testResults" :key="index" class="mb-1">
        {{ result }}
      </div>
    </div>
    
    <!-- 清空按钮 -->
    <div v-if="testResults.length > 0" class="mt-4">
      <button 
        @click="clearResults"
        class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
      >
        清空结果
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import * as api from '@/api/index.js'

export default {
  name: 'ApiTestDemo',
  setup() {
    const loading = ref(false)
    const testResults = ref([])
    const isLoggedIn = ref(false)
    
    // 计算属性
    const useMock = computed(() => import.meta.env.VITE_USE_MOCK === 'true')
    const baseUrl = computed(() => import.meta.env.VITE_API_BASE_URL)
    
    // 添加日志
    const addLog = (message, type = 'info') => {
      const timestamp = new Date().toLocaleTimeString()
      const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
      testResults.value.push(`[${timestamp}] ${prefix} ${message}`)
    }
    
    // 测试顾客端API
    const testCustomerApi = async () => {
      loading.value = true
      addLog('开始测试顾客端API...')
      
      try {
        // 测试获取商品列表
        addLog('测试获取商品列表...')
        const products = await api.getProducts({ page: 1, size: 5 })
        const productCount = products.length || products.records?.length || 0
        addLog(`获取商品列表成功: ${productCount}个商品`, 'success')
        
        // 测试获取品牌列表
        addLog('测试获取品牌列表...')
        const brands = await api.getBrands()
        addLog(`获取品牌列表成功: ${brands.length}个品牌`, 'success')
        
        // 测试搜索商品
        addLog('测试搜索商品...')
        const searchResults = await api.searchProducts('iPhone')
        const searchCount = searchResults.length || searchResults.records?.length || 0
        addLog(`搜索商品成功: ${searchCount}个结果`, 'success')
        
        addLog('顾客端API测试完成', 'success')
      } catch (error) {
        addLog(`顾客端API测试失败: ${error.message}`, 'error')
      } finally {
        loading.value = false
      }
    }
    
    // 测试商家登录
    const testMerchantLogin = async () => {
      loading.value = true
      addLog('开始测试商家登录...')
      
      try {
        const result = await api.loginByPhoneAndPassword('15035037444', 'password123')
        addLog(`商家登录成功: ${result.merchant?.storeName}`, 'success')
        isLoggedIn.value = true
      } catch (error) {
        addLog(`商家登录失败: ${error.message}`, 'error')
        isLoggedIn.value = false
      } finally {
        loading.value = false
      }
    }
    
    // 测试商家管理API
    const testMerchantManagement = async () => {
      if (!isLoggedIn.value) {
        addLog('请先登录', 'error')
        return
      }
      
      loading.value = true
      addLog('开始测试商家管理API...')
      
      try {
        // 测试获取商家信息
        addLog('测试获取商家信息...')
        const profile = await api.getMerchantProfile()
        addLog(`获取商家信息成功: ${profile.storeName}`, 'success')
        
        // 测试获取库存列表
        addLog('测试获取库存列表...')
        const inventory = await api.getInventory({ page: 1, size: 5 })
        const inventoryCount = inventory.length || inventory.records?.length || 0
        addLog(`获取库存列表成功: ${inventoryCount}个商品`, 'success')
        
        // 测试获取工作台数据
        addLog('测试获取工作台数据...')
        const dashboard = await api.getDashboardData()
        addLog(`获取工作台数据成功`, 'success')
        
        addLog('商家管理API测试完成', 'success')
      } catch (error) {
        addLog(`商家管理API测试失败: ${error.message}`, 'error')
      } finally {
        loading.value = false
      }
    }
    
    // 完整测试
    const runFullTest = async () => {
      clearResults()
      addLog('开始完整API测试...')
      addLog(`当前模式: ${useMock.value ? 'Mock模式' : '真实API模式'}`)
      addLog(`API基础URL: ${baseUrl.value}`)
      
      await testCustomerApi()
      await new Promise(resolve => setTimeout(resolve, 1000)) // 延迟1秒
      
      await testMerchantLogin()
      await new Promise(resolve => setTimeout(resolve, 1000)) // 延迟1秒
      
      if (isLoggedIn.value) {
        await testMerchantManagement()
      }
      
      addLog('完整API测试结束', 'success')
    }
    
    // 清空结果
    const clearResults = () => {
      testResults.value = []
    }
    
    // 组件挂载时显示配置信息
    onMounted(() => {
      addLog('API测试组件已加载')
      addLog(`当前模式: ${useMock.value ? 'Mock模式' : '真实API模式'}`)
      addLog(`API基础URL: ${baseUrl.value}`)
    })
    
    return {
      loading,
      testResults,
      isLoggedIn,
      useMock,
      baseUrl,
      testCustomerApi,
      testMerchantLogin,
      testMerchantManagement,
      runFullTest,
      clearResults
    }
  }
}
</script>

<style scoped>
.api-test-demo {
  max-width: 800px;
  margin: 0 auto;
}
</style>
