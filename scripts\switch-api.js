#!/usr/bin/env node

// File: scripts/switch-api.js
// API模式切换脚本

const fs = require('fs')
const path = require('path')

const ENV_FILE = path.join(__dirname, '../.env')

function readEnvFile() {
  if (!fs.existsSync(ENV_FILE)) {
    console.error('❌ .env文件不存在')
    process.exit(1)
  }
  
  return fs.readFileSync(ENV_FILE, 'utf8')
}

function writeEnvFile(content) {
  fs.writeFileSync(ENV_FILE, content, 'utf8')
}

function getCurrentMode() {
  const content = readEnvFile()
  const useMockMatch = content.match(/VITE_USE_MOCK=(.+)/)
  return useMockMatch ? useMockMatch[1].trim() === 'true' : true
}

function switchToMock() {
  let content = readEnvFile()
  content = content.replace(/VITE_USE_MOCK=.+/, 'VITE_USE_MOCK=true')
  writeEnvFile(content)
  console.log('✅ 已切换到Mock模式')
  console.log('💡 重启开发服务器以生效: npm run dev')
}

function switchToReal() {
  let content = readEnvFile()
  content = content.replace(/VITE_USE_MOCK=.+/, 'VITE_USE_MOCK=false')
  writeEnvFile(content)
  console.log('✅ 已切换到真实API模式')
  console.log('💡 请确保后端服务运行在 http://localhost:8080')
  console.log('💡 重启开发服务器以生效: npm run dev')
}

function showStatus() {
  const isMock = getCurrentMode()
  const content = readEnvFile()
  const baseUrlMatch = content.match(/VITE_API_BASE_URL=(.+)/)
  const baseUrl = baseUrlMatch ? baseUrlMatch[1].trim() : 'http://localhost:8080/api'
  
  console.log('=== 当前API配置 ===')
  console.log('模式:', isMock ? 'Mock模式' : '真实API模式')
  console.log('基础URL:', baseUrl)
  console.log('')
  console.log('=== 切换命令 ===')
  console.log('切换到Mock模式: npm run api:mock')
  console.log('切换到真实API: npm run api:real')
  console.log('查看当前状态: npm run api:status')
}

// 解析命令行参数
const command = process.argv[2]

switch (command) {
  case 'mock':
    switchToMock()
    break
  case 'real':
    switchToReal()
    break
  case 'status':
    showStatus()
    break
  default:
    console.log('API模式切换工具')
    console.log('')
    console.log('用法:')
    console.log('  node scripts/switch-api.js mock    # 切换到Mock模式')
    console.log('  node scripts/switch-api.js real    # 切换到真实API模式')
    console.log('  node scripts/switch-api.js status  # 查看当前状态')
    console.log('')
    console.log('或使用npm脚本:')
    console.log('  npm run api:mock')
    console.log('  npm run api:real')
    console.log('  npm run api:status')
}
