// File: src/config/api.js
// API配置文件

// API环境配置
export const API_CONFIG = {
  // 开发环境 - 本地后端服务
  development: {
    baseURL: 'http://localhost:8080/api',
    timeout: 10000,
    useMock: false
  },
  
  // Mock环境 - 使用Mock数据
  mock: {
    baseURL: '/api', // 不重要，因为使用Mock
    timeout: 10000,
    useMock: true
  },
  
  // 生产环境
  production: {
    baseURL: 'https://api.example.com/api',
    timeout: 10000,
    useMock: false
  }
}

// 获取当前环境配置
export function getCurrentConfig() {
  const env = import.meta.env.MODE || 'development'
  const useMock = import.meta.env.VITE_USE_MOCK === 'true'
  
  if (useMock) {
    return API_CONFIG.mock
  }
  
  return API_CONFIG[env] || API_CONFIG.development
}

// API端点配置
export const API_ENDPOINTS = {
  // 顾客端API
  CUSTOMER: {
    PRODUCTS: '/customer/products',
    PRODUCT_DETAIL: '/customer/products/:id',
    SEARCH: '/customer/products/search',
    HOT_PRODUCTS: '/customer/products/hot',
    BRANDS: '/customer/brands'
  },
  
  // 商家认证API
  MERCHANT_AUTH: {
    LOGIN: '/merchant/login',
    REGISTER: '/merchant/register',
    CHANGE_PASSWORD: '/merchant/change-password'
  },
  
  // 商家管理API
  MERCHANT: {
    PROFILE: '/merchant/profile',
    DASHBOARD: '/merchant/dashboard',
    INVENTORY: '/merchant/inventory',
    INVENTORY_ITEM: '/merchant/inventory/:id',
    SELL_ITEM: '/merchant/inventory/:id/sell',
    STATS_SALES: '/merchant/stats/sales',
    STATS_INVENTORY: '/merchant/stats/inventory',
    NOTIFICATIONS_READ: '/merchant/notifications/read'
  },
  
  // 文件管理API
  FILES: {
    UPLOAD: '/files/upload'
  }
}

// 构建URL的辅助函数
export function buildUrl(endpoint, params = {}) {
  let url = endpoint
  
  // 替换路径参数
  Object.keys(params).forEach(key => {
    url = url.replace(`:${key}`, params[key])
  })
  
  return url
}

// 错误码映射
export const ERROR_CODES = {
  200: '操作成功',
  400: '请求参数错误',
  401: '未认证或认证失败',
  403: '无权限访问',
  404: '资源不存在',
  500: '服务器内部错误'
}

// 获取错误信息
export function getErrorMessage(code, defaultMessage = '未知错误') {
  return ERROR_CODES[code] || defaultMessage
}
